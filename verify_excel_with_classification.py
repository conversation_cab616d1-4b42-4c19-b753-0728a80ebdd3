#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的Excel文件是否包含问题分类列
"""

import pandas as pd
import os

def verify_excel_classification():
    """验证Excel文件中的问题分类列"""
    print("验证Excel文件中的问题分类列")
    print("=" * 50)
    
    excel_file = "多阶段合规审查汇总.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"Excel文件不存在: {excel_file}")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print(f"Excel文件信息:")
        print(f"- 总行数: {len(df)}")
        print(f"- 总列数: {len(df.columns)}")
        print(f"- 列名: {list(df.columns)}")
        print()
        
        # 检查是否有问题分类列
        if '问题分类' in df.columns:
            print("✅ 问题分类列已成功添加")
            
            # 统计问题分类分布
            classification_counts = df['问题分类'].value_counts()
            print(f"\n问题分类分布:")
            print("-" * 30)
            for classification, count in classification_counts.items():
                if pd.notna(classification) and classification != '':
                    print(f"  {classification}: {count} 个")
                else:
                    print(f"  无分类: {count} 个")
            
            # 显示前几行数据
            print(f"\n前5行数据预览:")
            print("-" * 30)
            columns_to_show = ['问题', '问题分类', '合规评估结果', '不合规阶段']
            existing_columns = [col for col in columns_to_show if col in df.columns]
            
            for i, row in df.head().iterrows():
                print(f"\n第{i+1}行:")
                for col in existing_columns:
                    value = row[col]
                    if col == '问题' and pd.notna(value):
                        value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"  {col}: {value}")
            
            # 验证第一阶段不合规的文件是否没有问题分类
            print(f"\n验证第一阶段不合规文件:")
            print("-" * 30)
            stage1_failures = df[df['不合规阶段'] == '第一阶段']
            if len(stage1_failures) > 0:
                print(f"第一阶段不合规文件数: {len(stage1_failures)}")
                for i, row in stage1_failures.iterrows():
                    classification = row['问题分类']
                    problem = str(row['问题'])[:30] + "..." if len(str(row['问题'])) > 30 else str(row['问题'])
                    if pd.isna(classification) or classification == '':
                        print(f"  ✅ 行{i+1}: {problem} - 无分类（正确）")
                    else:
                        print(f"  ❌ 行{i+1}: {problem} - 有分类: {classification}（异常）")
            else:
                print("没有第一阶段不合规的文件")
            
            # 验证有问题分类的文件
            print(f"\n验证有问题分类的文件:")
            print("-" * 30)
            has_classification = df[df['问题分类'].notna() & (df['问题分类'] != '')]
            print(f"有问题分类的文件数: {len(has_classification)}")
            
            # 按分类统计不合规阶段
            if len(has_classification) > 0:
                print(f"\n各分类的不合规阶段分布:")
                for classification in has_classification['问题分类'].unique():
                    subset = has_classification[has_classification['问题分类'] == classification]
                    stage_counts = subset['不合规阶段'].value_counts()
                    print(f"  {classification}:")
                    for stage, count in stage_counts.items():
                        if pd.notna(stage) and stage != '':
                            print(f"    {stage}: {count} 个")
                        else:
                            print(f"    合规: {count} 个")
            
        else:
            print("❌ 问题分类列未找到")
            
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

def check_stage2_files_coverage():
    """检查第二阶段文件的覆盖情况"""
    print(f"\n检查第二阶段文件覆盖情况:")
    print("=" * 50)
    
    stage2_folder = "中间结果/第二阶段_问题分类"
    
    if not os.path.exists(stage2_folder):
        print("第二阶段文件夹不存在")
        return
    
    # 获取第二阶段文件列表
    stage2_files = [f for f in os.listdir(stage2_folder) if f.endswith('_stage2_result.json')]
    stage2_ids = [f.replace('_stage2_result.json', '') for f in stage2_files]
    stage2_ids.sort(key=lambda x: int(x) if x.isdigit() else float('inf'))
    
    print(f"第二阶段文件总数: {len(stage2_files)}")
    print(f"文件ID范围: {stage2_ids[0]} - {stage2_ids[-1]}")
    
    # 检查缺失的文件
    all_ids = [f"{i:03d}" for i in range(1, 102)]  # 001-101
    missing_ids = [id for id in all_ids if id not in stage2_ids]
    
    print(f"缺失的文件数: {len(missing_ids)}")
    if missing_ids:
        print(f"缺失的文件ID: {', '.join(missing_ids[:10])}")
        if len(missing_ids) > 10:
            print(f"... 还有 {len(missing_ids) - 10} 个")

if __name__ == "__main__":
    verify_excel_classification()
    check_stage2_files_coverage()
