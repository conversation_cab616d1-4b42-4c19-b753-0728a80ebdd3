#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检查Excel文件内容
"""

import pandas as pd

try:
    # 读取Excel文件
    df = pd.read_excel("多阶段合规审查汇总.xlsx")
    
    print("Excel文件基本信息:")
    print(f"行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    print(f"列名: {list(df.columns)}")
    
    # 检查问题分类列
    if '问题分类' in df.columns:
        print("\n✅ 问题分类列存在")
        print(f"问题分类统计:")
        classification_counts = df['问题分类'].value_counts()
        for classification, count in classification_counts.items():
            if pd.notna(classification) and classification != '':
                print(f"  {classification}: {count}")
            else:
                print(f"  无分类: {count}")
    else:
        print("\n❌ 问题分类列不存在")
    
    # 显示前几行
    print(f"\n前3行数据:")
    for i in range(min(3, len(df))):
        row = df.iloc[i]
        print(f"\n第{i+1}行:")
        print(f"  问题: {str(row['问题'])[:50]}...")
        print(f"  问题分类: {row.get('问题分类', 'N/A')}")
        print(f"  合规评估结果: {row.get('合规评估结果', 'N/A')}")
        print(f"  不合规阶段: {row.get('不合规阶段', 'N/A')}")

except Exception as e:
    print(f"读取Excel文件时出错: {e}")
