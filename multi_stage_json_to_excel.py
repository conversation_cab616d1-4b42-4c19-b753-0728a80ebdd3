#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多阶段合规审查结果JSON文件转Excel文件脚本
将多阶段合规审查结果的JSON文件转换为Excel汇总文件
"""

import pandas as pd
import json
import os
import re
from datetime import datetime
from pathlib import Path

def extract_compliance_judgment(evaluation_result):
    """
    从评估结果中提取最终合规判断
    
    Args:
        evaluation_result: 评估结果文本
        
    Returns:
        合规判断结果 (合规/不合规)
    """
    if not evaluation_result:
        return "未知"
    
    # 查找最终合规判断
    lines = evaluation_result.split('\n')
    for line in lines:
        if '**最终合规判断：**' in line:
            if '合规' in line:
                if '不合规' in line:
                    return "不合规"
                else:
                    return "合规"
    
    return "未知"

def extract_judgment_reason(evaluation_result):
    """
    从评估结果中提取判断理由
    
    Args:
        evaluation_result: 评估结果文本
        
    Returns:
        判断理由
    """
    if not evaluation_result:
        return ""
    
    # 查找判断理由部分
    lines = evaluation_result.split('\n')
    reason_text = ""
    
    # 寻找 **判断理由：** 部分
    for i, line in enumerate(lines):
        if '**判断理由：**' in line:
            # 提取该行的理由部分
            reason_part = line.split('**判断理由：**')
            if len(reason_part) > 1:
                reason_text = reason_part[1].strip()
            
            # 继续读取后续行，直到遇到下一个 ** 开头的行或空行
            for j in range(i + 1, len(lines)):
                next_line = lines[j].strip()
                if not next_line or next_line.startswith('**'):
                    break
                reason_text += " " + next_line
            break
    
    return reason_text.strip()

def extract_optimization_suggestions(evaluation_result):
    """
    从评估结果中提取优化建议

    Args:
        evaluation_result: 评估结果文本

    Returns:
        优化建议
    """
    if not evaluation_result:
        return ""

    # 查找优化建议部分
    lines = evaluation_result.split('\n')
    suggestion_text = ""

    # 寻找 **优化建议：** 部分
    for i, line in enumerate(lines):
        if '**优化建议：**' in line:
            # 提取该行的建议部分
            suggestion_part = line.split('**优化建议：**')
            if len(suggestion_part) > 1:
                suggestion_text = suggestion_part[1].strip()

            # 继续读取后续行，直到遇到下一个 ** 开头的行或空行
            for j in range(i + 1, len(lines)):
                next_line = lines[j].strip()
                if not next_line or next_line.startswith('**'):
                    break
                suggestion_text += " " + next_line
            break

    return suggestion_text.strip()

def extract_question_classification(file_id, stage2_folder="中间结果/第二阶段_问题分类"):
    """
    从第二阶段问题分类文件中提取问题分类

    Args:
        file_id: 文件ID（如 "001", "002"）
        stage2_folder: 第二阶段文件夹路径

    Returns:
        问题分类（question_type_raw字段的值）
    """
    try:
        # 构建第二阶段文件路径
        stage2_file = os.path.join(stage2_folder, f"{file_id}_stage2_result.json")

        # 检查文件是否存在
        if not os.path.exists(stage2_file):
            return ""  # 文件不存在，可能在第一阶段就被判定为不合规

        # 读取第二阶段文件
        with open(stage2_file, 'r', encoding='utf-8') as f:
            stage2_data = json.load(f)

        # 提取 question_type_raw 字段
        question_type_raw = stage2_data.get('question_type_raw', '')

        return question_type_raw

    except Exception as e:
        print(f"提取文件 {file_id} 的问题分类时出错: {e}")
        return ""

def multi_stage_json_to_excel(json_folder="多阶段合规审查结果/最终合规结果", output_file=None):
    """
    将多阶段合规审查结果JSON文件转换为Excel文件
    
    Args:
        json_folder: JSON文件夹路径
        output_file: 输出Excel文件路径
        
    Returns:
        转换成功的文件数量
    """
    try:
        # 检查文件夹是否存在
        if not os.path.exists(json_folder):
            print(f"错误: 文件夹不存在 - {json_folder}")
            return 0
        
        # 获取所有JSON文件，匹配 *_result.json 格式
        all_files = os.listdir(json_folder)
        json_files = [f for f in all_files if f.endswith('_result.json')]

        # 按文件名中的数字排序（如果是数字开头的话）
        def sort_key(filename):
            # 提取文件名前缀（去掉_result.json）
            prefix = filename.replace('_result.json', '')
            # 如果是纯数字，转换为整数排序；否则按字符串排序
            try:
                return (0, int(prefix))  # 数字文件排在前面
            except ValueError:
                return (1, prefix)  # 非数字文件排在后面

        json_files.sort(key=sort_key)
        
        if not json_files:
            print(f"错误: 在 {json_folder} 中没有找到JSON文件")
            return 0
        
        print(f"找到 {len(json_files)} 个JSON文件")
        
        # 准备数据列表
        data_list = []
        success_count = 0
        
        # 处理每个JSON文件
        for filename in json_files:
            try:
                filepath = os.path.join(json_folder, filename)

                # 读取JSON文件
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 提取文件ID（去掉_result.json后缀）
                file_id = filename.replace('_result.json', '')

                # 提取原始数据
                original_data = data.get('原始数据', {})

                # 提取合规判断和相关信息
                evaluation_result = data.get('合规评估结果', '')
                compliance_judgment = extract_compliance_judgment(evaluation_result)
                judgment_reason = extract_judgment_reason(evaluation_result)

                # 提取问题分类
                question_classification = extract_question_classification(file_id)

                # 构建Excel行数据，参考目标格式
                row_data = {
                    '类别': original_data.get('类别', ''),
                    '问题': original_data.get('问题', ''),
                    '回答': original_data.get('回答', ''),
                    '原因': original_data.get('原因', ''),
                    '是否可用': original_data.get('是否可用', ''),
                    '问题分类': question_classification,
                    '合规评估结果': f"** {compliance_judgment}",
                    'AI判断理由': judgment_reason,
                    '不合规阶段': data.get('不合规阶段', ''),
                }

                data_list.append(row_data)
                success_count += 1
                print(f"已处理: {filename} (问题分类: {question_classification if question_classification else '无'})")

            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                continue
        
        if not data_list:
            print("错误: 没有成功处理任何文件")
            return 0
        
        # 创建DataFrame
        df = pd.DataFrame(data_list)
        
        # 确保列的顺序
        column_order = [
            '类别', '问题', '回答', '原因', '是否可用', '问题分类',
            '合规评估结果', 'AI判断理由',  '不合规阶段',
        ]
        
        # 只保留存在的列
        existing_columns = [col for col in column_order if col in df.columns]
        df = df[existing_columns]
        
        # 生成输出文件名
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"多阶段合规审查汇总.xlsx"
        
        # 保存为Excel文件
        df.to_excel(output_file, index=False, engine='openpyxl')
        
        print(f"\n转换完成!")
        print(f"成功处理: {success_count} 个文件")
        print(f"输出文件: {output_file}")
        
        # 显示统计信息
        print(f"\n统计信息:")
        if '合规评估结果' in df.columns:
            # 统计合规判断结果
            compliance_counts = {}
            for result in df['合规评估结果']:
                if '合规' in str(result):
                    if '不合规' in str(result):
                        key = "不合规"
                    else:
                        key = "合规"
                else:
                    key = "未知"
                compliance_counts[key] = compliance_counts.get(key, 0) + 1
            
            for judgment, count in compliance_counts.items():
                print(f"  {judgment}: {count} 个")
        
        # 统计不合规阶段分布
        if '不合规阶段' in df.columns:
            print(f"\n不合规阶段分布:")
            stage_counts = df['不合规阶段'].value_counts()
            for stage, count in stage_counts.items():
                if stage and str(stage) != 'nan':
                    print(f"  {stage}: {count} 个")
        
        return success_count
        
    except Exception as e:
        print(f"转换过程中出错: {e}")
        return 0

def main():
    """主函数"""
    print("多阶段合规审查结果JSON转Excel转换器")
    print("=" * 60)
    
    # 默认配置
    json_dir = "多阶段合规审查结果/最终合规结果"
    output_file = None  # 自动生成文件名
    
    # 检查JSON文件夹是否存在
    if not os.path.exists(json_dir):
        print(f"错误: JSON文件夹不存在 - {json_dir}")
        print("请确保文件夹存在于当前目录中")
        return
    
    # 执行转换
    result = multi_stage_json_to_excel(json_dir, output_file)
    
    if result > 0:
        print(f"\n✓ 转换成功! 共处理 {result} 个JSON文件")
    else:
        print("\n✗ 转换失败!")

if __name__ == "__main__":
    main()
